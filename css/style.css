/* Base Styles */
:root {
    --primary-color: #00f0ff;
    --secondary-color: #7b00ff;
    --accent-color: #ff00aa;
    --dark-bg: #0a0a14;
    --darker-bg: #050508;
    --light-text: #f0f0f0;
    --medium-text: #a0a0b0;
    --glow-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
    --font-main: 'Space Grotesk', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-main);
    background-color: var(--dark-bg);
    color: var(--light-text);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Interactive Background */
#background-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -10;
    background-color: var(--darker-bg);
    pointer-events: none; /* Permet aux clics de passer à travers le canvas */
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 10;
}

/* Header & Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
    position: relative;
    z-index: 10;
}

.logo {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: var(--glow-shadow);
    letter-spacing: 1px;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--light-text);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    position: relative;
    transition: color 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80vh;
    padding: 50px 0;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.ai-orb {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, var(--primary-color), var(--secondary-color));
    box-shadow: 0 0 50px rgba(123, 0, 255, 0.5);
    position: relative;
    animation: pulse 4s infinite alternate;
}

.ai-orb::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    opacity: 0.5;
    animation: rotate 10s linear infinite;
}

.ai-orb::after {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    border: 1px solid var(--accent-color);
    opacity: 0.3;
    animation: rotate 15s linear infinite reverse;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 30px rgba(0, 240, 255, 0.5);
    }
    100% {
        transform: scale(1.05);
        box-shadow: 0 0 50px rgba(123, 0, 255, 0.7);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.glitch-text {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
    position: relative;
    animation: textShadow 2s infinite alternate;
}

@keyframes textShadow {
    0% {
        text-shadow: 0.05em 0 0 rgba(255, 0, 170, 0.75), -0.05em -0.025em 0 rgba(0, 240, 255, 0.75);
    }
    25% {
        text-shadow: -0.05em -0.025em 0 rgba(255, 0, 170, 0.75), 0.025em 0.025em 0 rgba(0, 240, 255, 0.75);
    }
    50% {
        text-shadow: 0.025em 0.05em 0 rgba(255, 0, 170, 0.75), 0.05em 0 0 rgba(0, 240, 255, 0.75);
    }
    75% {
        text-shadow: -0.025em -0.025em 0 rgba(255, 0, 170, 0.75), -0.025em -0.05em 0 rgba(0, 240, 255, 0.75);
    }
    100% {
        text-shadow: -0.025em 0 0 rgba(255, 0, 170, 0.75), 0.025em -0.025em 0 rgba(0, 240, 255, 0.75);
    }
}

.highlight {
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.subtitle {
    font-size: 1.5rem;
    color: var(--medium-text);
    margin-bottom: 40px;
}

.cta-container {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.cta-button {
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    font-family: var(--font-main);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    box-shadow: 0 4px 15px rgba(0, 240, 255, 0.3);
}

.primary:hover {
    box-shadow: 0 6px 20px rgba(0, 240, 255, 0.5);
    transform: translateY(-2px);
}

.secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 240, 255, 0.1);
}

.secondary:hover {
    box-shadow: 0 6px 20px rgba(0, 240, 255, 0.3);
    transform: translateY(-2px);
}

/* Section Styles */
section {
    padding: 100px 0;
    position: relative;
    z-index: 5;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 50px;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* About Section */
.about-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.about-content p {
    font-size: 1.2rem;
    max-width: 800px;
}

.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.tech-item {
    padding: 10px 22px;
    background: linear-gradient(145deg, rgba(10, 10, 20, 0.7), rgba(15, 15, 30, 0.9));
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--primary-color);
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 240, 255, 0.2), inset 0 0 0 1px rgba(0, 240, 255, 0.05);
    position: relative;
    overflow: hidden;
    outline: none;
    text-shadow: 0 0 5px rgba(0, 240, 255, 0.3);
    letter-spacing: 0.5px;
}

.tech-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 240, 255, 0.1), transparent);
    transition: all 0.5s ease;
}

.tech-item::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 22px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--primary-color));
    background-size: 400% 400%;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    animation: gradientBorder 3s ease infinite;
}

@keyframes gradientBorder {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.tech-item:hover {
    background: linear-gradient(145deg, rgba(0, 240, 255, 0.15), rgba(0, 240, 255, 0.05));
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 240, 255, 0.4);
    color: var(--light-text);
    border-color: transparent;
}

.tech-item:hover::before {
    left: 100%;
}

.tech-item:hover::after {
    opacity: 1;
}

.tech-item:active {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 5px rgba(0, 240, 255, 0.3);
    background: rgba(0, 240, 255, 0.2);
    transition: all 0.1s ease;
}

.tech-item:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 240, 255, 0.3), 0 5px 15px rgba(0, 240, 255, 0.4);
}

/* Ripple effect for tech-item buttons */

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
    z-index: 1;
}

@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

/* Text fade-in animation */
.fade-in-text {
    opacity: 0;
    animation: simpleFadeIn 0.5s ease forwards;
}

@keyframes simpleFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.project-card {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 240, 255, 0.1);
    padding-bottom: 20px;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.project-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
    position: relative;
}

.project-link[href^="http"]::after {
    content: '↗';
    position: absolute;
    top: 20px;
    right: 20px;
    color: var(--primary-color);
    font-size: 1.2rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.project-link[href^="http"]:hover::after {
    opacity: 1;
    transform: translate(2px, -2px);
}

.project-image {
    height: 200px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    position: relative;
    overflow: hidden;
}

.project-image-custom {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.project-image-custom::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.05) 10px,
        rgba(255, 255, 255, 0.05) 20px
    );
    animation: movePattern 20s linear infinite;
}

.project-image-custom::after {
    content: attr(data-project-name);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 1px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
    z-index: 1;
    transition: all 0.3s ease;
}

.project-card:hover .project-image-custom::after {
    background: rgba(0, 0, 0, 0.8);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.4);
    transform: translate(-50%, -50%) scale(1.1);
    letter-spacing: 2px;
}

/* Specific project styles */
.yachef-image {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
}

.sacorp-image {
    background: linear-gradient(45deg, #4158D0, #C850C0);
}

.voyage-image {
    background: linear-gradient(45deg, #56CCF2, #2F80ED);
}

.project-image::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.05) 10px,
        rgba(255, 255, 255, 0.05) 20px
    );
    animation: movePattern 20s linear infinite;
}

@keyframes movePattern {
    0% {
        transform: translate(-10%, -10%) rotate(0deg);
    }
    100% {
        transform: translate(-10%, -10%) rotate(360deg);
    }
}

.project-card h3 {
    padding: 20px 20px 10px;
    font-size: 1.3rem;
    color: var(--primary-color);
}

.project-card p {
    padding: 0 20px;
    color: var(--medium-text);
    margin-bottom: 15px;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 0 20px;
}

.project-tags span {
    padding: 4px 10px;
    background: rgba(0, 240, 255, 0.1);
    border-radius: 15px;
    font-size: 0.8rem;
    color: var(--primary-color);
}

/* Contact Section */
.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-content p {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.2rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    background: rgba(10, 10, 20, 0.5);
    border: 1px solid rgba(0, 240, 255, 0.3);
    border-radius: 5px;
    color: var(--light-text);
    font-family: var(--font-main);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
}

.contact-form .cta-button {
    align-self: center;
    margin-top: 20px;
}

/* Footer */
.footer {
    background: var(--darker-bg);
    padding: 40px 0;
    margin-top: 50px;
    border-top: 1px solid rgba(0, 240, 255, 0.1);
    position: relative;
    z-index: 10;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.social-links {
    display: flex;
    gap: 20px;
}

.social-link {
    color: var(--medium-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero {
        flex-direction: column;
        text-align: center;
        gap: 50px;
    }

    .hero-content {
        max-width: 100%;
    }

    .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .about-content {
        text-align: center;
    }

    .tech-stack {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
    }

    .glitch-text {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1.2rem;
    }

    .ai-orb {
        width: 200px;
        height: 200px;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .nav ul {
        gap: 15px;
    }

    .cta-container {
        flex-direction: column;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }
}
