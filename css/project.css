/* Project Page Styles */

/* Active navigation link */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Project Hero Section */
.project-hero {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 80px 0 40px;
}

.project-hero-content {
    max-width: 800px;
}

.project-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
    border-radius: 20px;
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2.5rem;
    font-weight: bold;
    color: white;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.project-subtitle {
    font-size: 1.5rem;
    color: var(--medium-text);
    margin-bottom: 40px;
}

.project-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.project-link-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-link-button.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    box-shadow: 0 4px 15px rgba(0, 240, 255, 0.3);
}

.project-link-button.primary:hover {
    box-shadow: 0 6px 20px rgba(0, 240, 255, 0.5);
    transform: translateY(-2px);
}

.project-link-button.secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 240, 255, 0.1);
}

.project-link-button.secondary:hover {
    box-shadow: 0 6px 20px rgba(0, 240, 255, 0.3);
    transform: translateY(-2px);
}

/* Animation de la flèche dans les boutons */
.project-link-button span {
    display: inline-block;
    transition: transform 0.3s ease;
}

.project-link-button:hover span {
    transform: translateX(3px);
}

/* Project Sections */
.project-section {
    padding: 80px 0;
    position: relative;
}

.project-section-title {
    font-size: 2rem;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.project-section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.project-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.project-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--medium-text);
}

.project-content p strong {
    color: var(--light-text);
}

/* Features Section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.feature-card {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    padding: 30px;
    border: 1px solid rgba(0, 240, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(10, 10, 20, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    border: 1px solid var(--primary-color);
    font-size: 1.5rem;
    color: var(--primary-color);
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-card p {
    color: var(--medium-text);
    font-size: 1rem;
    line-height: 1.6;
}

/* Technologies Section */
.tech-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.tech-badge {
    padding: 8px 16px;
    background: rgba(10, 10, 20, 0.5);
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.tech-badge:hover {
    background: rgba(0, 240, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 240, 255, 0.3);
}

/* Gallery Section */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.gallery-item {
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    height: 200px;
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.03);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    opacity: 0.8;
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.9rem;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-caption {
    transform: translateY(0);
}

/* Process Section */
.process-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px 0;
}

.process-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50px;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    z-index: 1;
}

.process-step {
    position: relative;
    margin-bottom: 50px;
    padding-left: 100px;
}

.process-dot {
    position: absolute;
    left: 44px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
    z-index: 2;
}

.process-content {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    padding: 25px;
    position: relative;
    border: 1px solid rgba(0, 240, 255, 0.1);
    transition: all 0.3s ease;
}

.process-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.process-step h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.process-step p {
    color: var(--medium-text);
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    text-align: center;
    padding: 100px 0;
}

.cta-content {
    max-width: 700px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    color: var(--medium-text);
    margin-bottom: 40px;
}

/* Style pour la flèche dans les boutons */
.cta-button {
    position: relative;
    overflow: hidden;
}

/* Correction pour l'animation de la flèche dans les boutons */
.cta-button span {
    display: inline-block;
    transition: transform 0.3s ease;
}

.cta-button:hover span {
    transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .project-section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .project-content {
        text-align: center;
    }

    .process-timeline::before {
        left: 30px;
    }

    .process-dot {
        left: 24px;
    }

    .process-step {
        padding-left: 70px;
    }
}

@media (max-width: 768px) {
    .project-hero {
        padding: 60px 0 30px;
    }

    .project-logo {
        width: 100px;
        height: 100px;
        font-size: 2rem;
    }

    .glitch-text {
        font-size: 2.5rem;
    }

    .project-subtitle {
        font-size: 1.2rem;
    }

    .project-links {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .gallery-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .process-content {
        padding: 15px;
    }

    .tech-grid {
        justify-content: center;
    }
}
