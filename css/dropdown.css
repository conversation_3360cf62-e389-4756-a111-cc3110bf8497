/* Dropdown Menu Styles */
.nav-item {
    position: relative;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--darker-bg);
    min-width: 200px;
    border-radius: 5px;
    padding: 10px 0;
    z-index: 100;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 240, 255, 0.2);
    border: 1px solid rgba(0, 240, 255, 0.1);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.nav-item:hover .dropdown-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 8px 20px;
    color: var(--light-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.dropdown-item:hover {
    background-color: rgba(0, 240, 255, 0.1);
    color: var(--primary-color);
    padding-left: 25px;
}

.dropdown-item.active {
    color: var(--primary-color);
    background-color: rgba(0, 240, 255, 0.05);
}

/* Pas d'indicateur visuel pour le dropdown */
.dropdown-toggle::after {
    content: none;
}

/* Responsive styles */
@media (max-width: 768px) {
    .dropdown-menu {
        position: static;
        background-color: transparent;
        box-shadow: none;
        border: none;
        padding: 0 0 0 20px;
        min-width: auto;
        display: none;
        opacity: 1;
        transform: none;
    }

    .nav-item:hover .dropdown-menu {
        display: block;
    }

    .dropdown-item {
        padding: 8px 10px;
    }

    .dropdown-item:hover {
        background-color: transparent;
        padding-left: 15px;
    }
}
