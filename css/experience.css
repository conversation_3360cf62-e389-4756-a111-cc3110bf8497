/* Experience Page Styles */

/* Active navigation link */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Experience Hero Section */
.experience-hero {
    min-height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 80px 0 40px;
}

.experience-hero-content {
    max-width: 800px;
}

/* Experience Sections */
.experience-section {
    padding: 60px 0;
    position: relative;
}

/* Timeline Styles */
.timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50px;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    z-index: 1;
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
    padding-left: 100px;
}

.timeline-dot {
    position: absolute;
    left: 44px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
    z-index: 2;
}

.timeline-content {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    padding: 25px;
    position: relative;
    border: 1px solid rgba(0, 240, 255, 0.1);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.timeline-date {
    display: inline-block;
    padding: 5px 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--dark-bg);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.timeline-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.4rem;
}

.timeline-content p {
    margin-bottom: 10px;
    color: var(--medium-text);
}

.timeline-content p:first-of-type {
    font-weight: 500;
    color: var(--light-text);
}

.experience-details {
    margin-top: 15px;
    padding-left: 20px;
}

.experience-details li {
    margin-bottom: 8px;
    position: relative;
    color: var(--medium-text);
}

.experience-details li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: -15px;
}

/* Skills Styles */
.skills-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.skills-category {
    margin-bottom: 20px;
}

.skills-category h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    position: relative;
    display: inline-block;
}

.skills-category h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.skill-item {
    margin-bottom: 15px;
}

.skill-name {
    margin-bottom: 8px;
    font-weight: 500;
}

.skill-bar {
    height: 8px;
    background: rgba(10, 10, 20, 0.5);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-level {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    position: relative;
    animation: skillFill 1.5s ease-out forwards;
    transform-origin: left;
}

@keyframes skillFill {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

/* Languages Section */
.languages-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.language-item {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(0, 240, 255, 0.1);
    transition: all 0.3s ease;
}

.language-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.language-item h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.language-level {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-bottom: 10px;
}

.language-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(0, 240, 255, 0.2);
}

.language-dot.active {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.language-item p {
    color: var(--medium-text);
    font-size: 0.9rem;
}

/* Certifications Section */
.certifications-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.certification-item {
    background: rgba(10, 10, 20, 0.5);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    border: 1px solid rgba(0, 240, 255, 0.1);
    transition: all 0.3s ease;
}

.certification-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 240, 255, 0.2);
    border-color: var(--primary-color);
}

.certification-icon {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(10, 10, 20, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--primary-color);
}

.certification-badge {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    position: relative;
}

.certification-badge::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--dark-bg);
    font-weight: bold;
    font-size: 1.2rem;
}

.certification-content h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.certification-issuer {
    color: var(--light-text);
    font-weight: 500;
    margin-bottom: 5px;
}

.certification-date {
    color: var(--medium-text);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .timeline::before {
        left: 30px;
    }
    
    .timeline-dot {
        left: 24px;
    }
    
    .timeline-item {
        padding-left: 70px;
    }
    
    .certifications-container,
    .languages-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .certifications-container,
    .languages-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .timeline-content {
        padding: 15px;
    }
    
    .certification-item {
        flex-direction: column;
        text-align: center;
    }
    
    .certification-icon {
        margin-bottom: 10px;
    }
}
