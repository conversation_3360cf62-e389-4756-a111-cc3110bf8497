<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio IA | Tym</title>
    <meta name="description" content="Portfolio de Tym, développeur fullstack spécialisé en intelligence artificielle et développement backend. Découvrez mes projets, expériences et compétences.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dropdown.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="icon" href="assets/favicon2.ico" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Interactive Background Canvas -->
    <canvas id="background-canvas"></canvas>

    <!-- Main Content -->
    <main class="container">
        <header class="header">
            <a href="index.html" class="logo" style="text-decoration: none;">TYM.IA</a>
            <nav class="nav">
                <ul>
                    <li><a href="index.html" class="nav-link active">Home</a></li>
                    <li><a href="experience.html" class="nav-link">Expériences</a></li>
                    <li class="nav-item">
                        <a href="#personal-projects" class="nav-link dropdown-toggle">Projets</a>
                        <div class="dropdown-menu">
                            <a href="yachef.html" class="dropdown-item">YaChef</a>
                            <a href="sacorp.html" class="dropdown-item">SAcorp</a>
                            <a href="voyage.html" class="dropdown-item">Stop Arnaque Voyage</a>
                        </div>
                    </li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
        </header>

        <section class="hero">
            <div class="hero-content">
                <h1 class="glitch-text">Intelligence Artificielle & <span class="highlight">Dévellopement</span></h1>
                <p class="subtitle">Developpeur fullstack spécialisé en IA</p>
                <div class="cta-container">
                    <a href="#personal-projects" class="cta-button primary">Découvrir mes projets</a>
                    <a href="contact.html" class="cta-button secondary">Me contacter</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="ai-orb">
                    <img src="assets/img/photo-tym.png" alt="Photo de Tym" class="tym-photo">
                </div>
            </div>
        </section>

        <section id="about" class="about-section">
            <h2 class="section-title">À propos</h2>
            <div class="about-content">
                <p id="about-text">Bienvenue dans mon univers où l'intelligence artificielle rencontre la créativité. Je développe des solutions innovantes qui repoussent les limites de la technologie.</p>
                <div class="tech-stack">
                    <button class="tech-item" onclick="changeAboutText('IA')">Intelligence Artificielle</button>
                    <button class="tech-item" onclick="changeAboutText('BK')">Backend</button>
                    <button class="tech-item" onclick="changeAboutText('FR')">Frontend</button>
                    <button class="tech-item" onclick="changeAboutText('FS')">Fullstack</button>
                    <button class="tech-item" onclick="changeAboutText('Web')">Web Development</button>
                </div>
            </div>
        </section>

        <script>
        function changeAboutText(tech) {
            const aboutText = document.getElementById('about-text');
            switch(tech) {
                case 'IA':
                    aboutText.textContent = "L'Intelligence Artificielle est au cœur de mes projets.";
                    break;
                case 'BK':
                    aboutText.textContent = "Je développe des architectures backend robustes et évolutives, en utilisant les dernières technologies pour garantir performance et sécurité.";
                    break;
                case 'FR':
                    aboutText.textContent = "Je conçois des interfaces frontend modernes et réactives, en utilisant les frameworks les plus récents pour une expérience utilisateur optimale.";
                    break;
                case 'Web':
                    aboutText.textContent = "Mon approche en développement web se concentre sur la création d'applications complètes, du backend au frontend, avec une attention particulière à l'expérience utilisateur.";
                    break;
                case 'FS':
                    aboutText.textContent = "En tant que développeur fullstack, je maîtrise l'ensemble de la pile technologique, du backend au frontend, en passant par les bases de données et le déploiement, pour créer des applications web complètes et performantes.";
                    break;
            }
        }
        </script>

        <section id="personal-projects" class="projects-section">
            <h2 class="section-title">Projets</h2>
            <div class="projects-grid">
                <!-- Project cards will be added here -->
                <div class="project-card">
                    <a href="yachef.html" class="project-link">
                        <div class="project-image project-image-custom yachef-image" data-project-name="YaChef.ai"></div>
                        <h3>YaChef</h3>
                        <p>Assistant de cuisine intelligent propulsé par l'IA qui révolutionne l'expérience culinaire.</p>
                        <div class="project-tags">
                            <span>IA</span>
                            <span>Assistant Culinaire</span>
                            <span>Web App</span>
                        </div>
                    </a>
                </div>
                <div class="project-card">
                    <a href="sacorp.html" class="project-link">
                        <div class="project-image project-image-custom sacorp-image" data-project-name="SAcorp"></div>
                        <img src="assets/img/logo-SAcorp.png" alt="SAcorp Logo" class="project-logo" style="position: absolute; top: 0; left: 25%; padding: 10px; height: 50%;">
                        <h3>SAcorp site web</h3>
                        <p>Design et création du site web de SAcorp</p>
                        <div class="project-tags">
                            <span>Web Design</span>
                            <span>Frontend</span>
                            <span>Site Vitrine</span>
                        </div>
                    </a>
                </div>
                <div class="project-card">
                    <a href="voyage.html" class="project-link">
                        <div class="project-image project-image-custom voyage-image" data-project-name="StopArnaqueVoyage"></div>
                        <img src="assets/img/logo-Voyage.png" alt="Stop arnaque voyage Logo" class="project-logo" style="position: absolute; top: 0; left: 0; padding: 10px; width: 100%;">
                        <h3>Stop arnaque voyage</h3>
                        <p>Aide au developpement du site stop arnaque voyage avec l'equipe de développement SAcorp</p>
                        <div class="project-tags">
                            <span>Web Design</span>
                            <span>Frontend</span>
                            <span>Site Vitrine</span>
                        </div>
                    </a>
                </div>
                <!-- Espace pour ajouter d'autres projets avec leurs pages de présentation -->
            </div>
        </section>
<style>
.experiences-section {
    padding: 2rem 0;
}

.experiences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50%, 1fr));
    gap: 1.5rem;
}

.experience-card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.experience-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.experience-card h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.experience-company {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.experience-date {
    font-style: italic;
    color: #888;
    margin-bottom: 0.5rem;
}
</style>

        <section id="experiences" class="experiences-section">
            <h2 class="section-title">Expériences</h2>
            <div class="experiences-grid">
                <div class="experience-card">
                    <h3>Développeur Fullstack IA</h3>
                    <p class="experience-company">Diptick</p>
                    <p class="experience-date">2024 - Présent</p>
                    <p>Développement complet de solutions IA personnalisées pour des clients variés.</p>
                    <ul>
                        <li>Backends robustes et scalables</li>
                        <li>Frontends modernes et interactifs</li>
                        <li>CI/CD, base de données, performances serveurs</li>
                    </ul>
                </div>
                <div class="experience-card">
                    <h3>Développeur Web & Backend Freelance</h3>
                    <p class="experience-company">SAcorp</p>
                    <p class="experience-date">2023 - 2024</p>
                    <p>Création de sites professionnels (corporate et prévention), et développement d’une plateforme SaaS.</p>
                    <ul>
                        <li>Design responsive & SEO</li>
                        <li>Fonctionnalités interactives</li>
                        <li>Sécurité & accessibilité</li>
                        <li>Conception et déploiement d’un SaaS : architecture backend, gestion des utilisateurs et scalabilité</li>
                    </ul>

                </div>
                <div class="experience-card">
                    <h3>Responsable IA & Algorithmes Automatiques</h3>
                    <p class="experience-company">L'École (Startup)</p>
                    <p class="experience-date">2021 - 2023</p>
                    <p>Développement d’une application iPad pour créer des IA personnalisées.</p>
                    <ul>
                        <li>Algorithmes IA sur-mesure pour iPad</li>
                        <li>Infrastructure AWS, optimisation mobile</li>
                        <li>Pitch produit aux investisseurs</li>
                    </ul>
                </div>
                <div class="experience-card">
                    <h3>Stage VR & Aérospatial</h3>
                    <p class="experience-company">Thales Alenia Space</p>
                    <p class="experience-date">2021</p>
                    <p>Conception d’un système interactif en réalité virtuelle pour la conception de systèmes spatiaux.</p>
                </div>
            </div>
            <div class="cta-container" style="justify-content: center;">
                <a href="experience.html" class="cta-button primary">Voir toutes mes experiences</a>
            </div>
            
        </section>

        <section id="contact" class="contact-section">
            <h2 class="section-title">Contact</h2>
            <div class="contact-content">
                <p>Intéressé par une collaboration ou simplement curieux ? N'hésitez pas à me contacter.</p>
                <div class="cta-container" style="justify-content: center;">
                    <a href="contact.html" class="cta-button primary">Me contacter</a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 TYM.IA - Tous droits réservés</p>
            <div class="social-links">
                <a href="contact.html" class="social-link">Contact</a>
            </div>
        </div>
    </footer>

    <script src="js/background.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
