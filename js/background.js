// Interactive Background Canvas
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('background-canvas');
    const ctx = canvas.getContext('2d');

    // Variables pour le défilement et le chaos
    let scrollY = window.scrollY || window.pageYOffset;
    let chaosLevel = 0; // Niveau de chaos (0-1)
    let currentSection = 'hero'; // Section actuelle

    // Set canvas size
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Suivre le défilement et calculer le niveau de chaos
    window.addEventListener('scroll', () => {
        scrollY = window.scrollY || window.pageYOffset;

        // Calculer la hauteur totale de la page (sans le viewport)
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;

        // Calculer le niveau de chaos (0 en haut, 1 en bas)
        chaosLevel = Math.min(scrollY / scrollHeight, 1);

        // Déterminer la section actuelle
        const aboutSection = document.getElementById('about');
        const projectsSection = document.getElementById('projects');
        const contactSection = document.getElementById('contact');

        if (scrollY < aboutSection.offsetTop - 100) {
            currentSection = 'hero';
        } else if (scrollY < projectsSection.offsetTop - 100) {
            currentSection = 'about';
        } else if (scrollY < contactSection.offsetTop - 100) {
            currentSection = 'projects';
        } else {
            currentSection = 'contact';
        }
    });

    // Particle system
    class Particle {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.size = Math.random() * 3 + 1;
            this.originalSize = this.size; // Stocker la taille originale pour l'interaction
            this.baseSpeedX = Math.random() * 2 - 1;
            this.baseSpeedY = Math.random() * 2 - 1;
            this.speedX = this.baseSpeedX;
            this.speedY = this.baseSpeedY;
            // Couleur fixe pour chaque particule pour éviter le scintillement
            this.hue = this.getHueForSection(currentSection);
            this.color = `hsl(${this.hue}, 100%, 50%)`;
            this.life = Math.random() * 100 + 100; // Durée de vie plus longue
            this.opacity = 1;
            this.angle = Math.random() * Math.PI * 2;
            this.angleSpeed = Math.random() * 0.01 - 0.005; // Vitesse de rotation réduite
            // ID unique pour cette particule
            this.id = Math.floor(Math.random() * 10000);
            // Propriétés pour l'interaction avec l'onde de clic
            this.interacted = false;
        }

        getHueForSection(section) {
            // Valeur de teinte fixe pour chaque particule
            switch(section) {
                case 'hero':
                    return Math.random() * 60 + 180; // Bleu/cyan
                case 'about':
                    return Math.random() * 60 + 120; // Vert/cyan
                case 'projects':
                    return Math.random() * 60 + 240; // Bleu/violet
                case 'contact':
                    return Math.random() * 60 + 300; // Violet/rose
                default:
                    return Math.random() * 60 + 180;
            }
        }

        update() {
            // Mouvement plus stable pour éviter le scintillement
            const chaos = chaosLevel * 1.5; // Réduire l'effet de chaos

            // Mouvement chaotique basé sur le niveau de chaos
            if (chaosLevel > 0.1) {
                // Utiliser l'ID comme graine pour un mouvement plus stable
                this.angle += this.angleSpeed * (1 + chaosLevel * 2);

                // Facteurs de chaos plus stables
                const chaosFactorX = Math.sin(this.angle) * chaos;
                const chaosFactorY = Math.cos(this.angle) * chaos;

                this.speedX = this.baseSpeedX + chaosFactorX * 0.5;
                this.speedY = this.baseSpeedY + chaosFactorY * 0.5;
            } else {
                this.speedX = this.baseSpeedX;
                this.speedY = this.baseSpeedY;
            }

            // Mettre à jour la position
            this.x += this.speedX;
            this.y += this.speedY;
            this.life--;
            this.opacity = this.life / 150;

            // Mettre à jour la couleur uniquement lors du changement de section
            // pour éviter le scintillement
            if (this.currentSection !== currentSection) {
                this.currentSection = currentSection;
                this.hue = this.getHueForSection(currentSection);
                this.color = `hsl(${this.hue}, 100%, 50%)`;
            }

            // Boundary check avec rebond plus stable
            if (this.x < 0 || this.x > canvas.width) {
                this.speedX *= -1;
                // Éviter les changements aléatoires qui causent le scintillement
                if (chaosLevel > 0.5 && this.id % 3 === 0) {
                    this.speedY *= 1 + (chaosLevel * 0.2);
                }
            }
            if (this.y < 0 || this.y > canvas.height) {
                this.speedY *= -1;
                if (chaosLevel > 0.5 && this.id % 3 === 0) {
                    this.speedX *= 1 + (chaosLevel * 0.2);
                }
            }
        }

        draw() {
            ctx.beginPath();

            // Formes plus stables pour éviter le scintillement
            if (chaosLevel > 0.7 && currentSection !== 'contact') {
                // Formes polygonales pour chaos élevé, mais avec moins de variation aléatoire
                const sides = Math.floor(3 + chaosLevel * 4);
                ctx.moveTo(this.x + this.size * Math.cos(0), this.y + this.size * Math.sin(0));

                for (let i = 1; i <= sides; i++) {
                    const angle = i * 2 * Math.PI / sides;
                    // Utiliser l'ID comme graine pour une variation stable
                    const radiusVariation = 1 + ((this.id % 10) / 10) * chaosLevel * 0.3;
                    ctx.lineTo(
                        this.x + this.size * radiusVariation * Math.cos(angle),
                        this.y + this.size * radiusVariation * Math.sin(angle)
                    );
                }
            } else {
                // Cercles simples pour un chaos faible ou section contact
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            }

            ctx.fillStyle = this.color.replace(')', `, ${this.opacity})`);
            ctx.fill();
        }
    }

    // Connection lines - Version futuriste
    function drawConnections(particles) {
        // Effet de connexions plus visible au niveau du footer (bas de page)

        // Augmenter la distance de connexion avec le défilement
        const baseConnectionDistance = 200; // Distance de base augmentée

        // Connexions éparpillées qui deviennent plus visibles avec le défilement
        let connectionDistance, connectionProbability, maxConnections, baseOpacity;

        // Même traitement pour toutes les sections, y compris contact
        // Augmenter la visibilité des connexions
        connectionDistance = baseConnectionDistance * (0.6 + chaosLevel * 0.5); // Distance augmentée
        connectionProbability = 0.015 + (chaosLevel * 0.06); // Probabilité légèrement augmentée
        maxConnections = Math.floor(2 + chaosLevel * 3); // 2 à 5 connexions max (augmenté)
        baseOpacity = 0.15 + (chaosLevel * 0.25); // Opacité augmentée

        // Utiliser un ID de particule pour stabiliser les connexions et éviter le scintillement
        for (let i = 0; i < particles.length; i++) {
            // Utiliser l'index de la particule comme graine pour la décision de connexion
            const shouldConnect = (i % Math.floor(1 / connectionProbability)) === 0;

            if (!shouldConnect) continue;

            let connections = 0;

            for (let j = 0; j < particles.length; j++) {
                if (i === j) continue;

                // Utiliser une fonction déterministe basée sur les indices
                const shouldConnectPair = ((i + j) % Math.floor(1 / connectionProbability)) === 0;

                if (!shouldConnectPair) continue;

                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < connectionDistance && connections < maxConnections) {
                    connections++;

                    // Couleur des connexions basée sur la section actuelle avec effet de gradient
                    let startColor, endColor;
                    // Utiliser une opacité plus stable pour éviter le scintillement
                    const opacityFactor = baseOpacity * (1 - distance / connectionDistance);

                    switch(currentSection) {
                        case 'hero':
                            startColor = `rgba(0, 240, 255, ${opacityFactor})`;
                            endColor = `rgba(0, 150, 255, ${opacityFactor * 0.7})`;
                            break;
                        case 'about':
                            startColor = `rgba(0, 255, 128, ${opacityFactor})`;
                            endColor = `rgba(0, 200, 100, ${opacityFactor * 0.7})`;
                            break;
                        case 'projects':
                            startColor = `rgba(128, 0, 255, ${opacityFactor})`;
                            endColor = `rgba(180, 0, 200, ${opacityFactor * 0.7})`;
                            break;
                        case 'contact':
                            startColor = `rgba(255, 0, 128, ${opacityFactor})`;
                            endColor = `rgba(200, 0, 100, ${opacityFactor * 0.7})`;
                            break;
                        default:
                            startColor = `rgba(0, 240, 255, ${opacityFactor})`;
                            endColor = `rgba(0, 150, 255, ${opacityFactor * 0.7})`;
                    }

                    // Effet de pulsation basé sur le temps pour un aspect futuriste
                    const pulseSpeed = 0.002;
                    const pulseIntensity = 0.2;
                    const pulseFactor = 1 + Math.sin(Date.now() * pulseSpeed) * pulseIntensity;

                    // Créer un gradient pour la ligne
                    const gradient = ctx.createLinearGradient(
                        particles[i].x, particles[i].y,
                        particles[j].x, particles[j].y
                    );
                    gradient.addColorStop(0, startColor);
                    gradient.addColorStop(1, endColor);

                    ctx.beginPath();
                    ctx.strokeStyle = gradient;

                    // Épaisseur de ligne variable pour effet futuriste - augmentée pour plus de visibilité
                    const baseWidth = 0.7 + (chaosLevel * 0.8);
                    const lineWidth = baseWidth * pulseFactor;
                    ctx.lineWidth = lineWidth;

                    // Effet de ligne futuriste
                    // Lignes en pointillés pour toutes les sections - motif ajusté pour plus de visibilité
                    ctx.setLineDash([6 * pulseFactor, 7 * (1 - chaosLevel * 0.5)]);

                    // Ajouter un effet de lueur (glow) pour aspect futuriste - augmenté pour plus de visibilité
                    ctx.shadowBlur = 8 * pulseFactor;
                    ctx.shadowColor = startColor.replace(/(\d+)\)$/, (_, opacity) => {
                        return Math.min(parseFloat(opacity) * 1.8, 1) + ')';
                    });

                    // Tracer la ligne
                    ctx.moveTo(particles[i].x, particles[i].y);

                    // Ligne droite ou courbe selon le niveau de chaos
                    if (chaosLevel > 0.4 && Math.random() < chaosLevel * 0.3) {
                        // Ligne avec légère courbure pour aspect futuriste
                        const midX = (particles[i].x + particles[j].x) / 2;
                        const midY = (particles[i].y + particles[j].y) / 2;
                        const offset = ((i * j) % 20) * chaosLevel * 0.5; // Offset déterministe

                        ctx.quadraticCurveTo(
                            midX + offset,
                            midY - offset,
                            particles[j].x,
                            particles[j].y
                        );
                    } else {
                        ctx.lineTo(particles[j].x, particles[j].y);
                    }

                    ctx.stroke();

                    // Réinitialiser les effets pour ne pas affecter les autres éléments
                    ctx.shadowBlur = 0;
                    ctx.setLineDash([]);

                    // Ajouter des points de connexion aux extrémités pour un aspect réseau futuriste
                    if (distance < connectionDistance * 0.5) {
                        const nodeSize = 1.0 * pulseFactor;

                        // Point de connexion au milieu de la ligne
                        const midX = (particles[i].x + particles[j].x) / 2;
                        const midY = (particles[i].y + particles[j].y) / 2;

                        ctx.beginPath();
                        ctx.arc(midX, midY, nodeSize, 0, Math.PI * 2);
                        ctx.fillStyle = startColor.replace(/(\d+)\)$/, (_, opacity) => {
                            return Math.min(parseFloat(opacity) * 1.5, 1) + ')';
                        });
                        ctx.fill();
                    }
                }
            }
        }
    }

    // Mouse interaction
    let mouse = {
        x: null,
        y: null,
        radius: 180,
        active: false,
        particles: [], // Stocker les particules générées par la souris
        lastClick: null, // Stocker la position du dernier clic
        clickWave: { // Propriétés pour l'animation de clic
            active: false,
            x: 0,
            y: 0,
            radius: 0,
            maxRadius: 450, // Rayon maximal augmenté
            speed: 3.5, // Vitesse réduite pour une animation plus douce
            color: 'rgba(0, 240, 255, 0.3)',
            startTime: 0,
            lastInteractionRadius: 0, // Pour suivre la dernière zone d'interaction
            fadeOutPhase: false // Indique si l'onde est en phase de disparition
        }
    };

    window.addEventListener('mousemove', (e) => {
        mouse.x = e.x;
        mouse.y = e.y;
        mouse.active = true;

        // Créer plus de particules lors du mouvement de la souris
        // Augmenter la fréquence de génération (1 chance sur 2)
        if (Math.random() < 0.5) {
            // Ajouter une légère variation de position pour un effet plus naturel
            const offsetX = (Math.random() - 0.5) * 20;
            const offsetY = (Math.random() - 0.5) * 20;
            const newParticle = new Particle(mouse.x + offsetX, mouse.y + offsetY);

            // Donner une durée de vie plus longue aux particules de la souris
            newParticle.life = Math.random() * 180 + 120;
            // Marquer cette particule comme générée par la souris
            newParticle.fromMouse = true;

            particles.push(newParticle);
            mouse.particles.push(newParticle);

            // Limiter le nombre de particules de souris pour éviter les problèmes de performance
            if (mouse.particles.length > 20) { // Limite augmentée de 15 à 20
                mouse.particles.shift();
            }
        }

        // Désactiver l'état actif après un court délai
        setTimeout(() => {
            mouse.active = false;
        }, 100);
    });

    // Animation au clic de la souris
    window.addEventListener('click', (e) => {
        // Enregistrer la position du clic
        mouse.lastClick = { x: e.x, y: e.y };

        // Activer l'onde de choc
        mouse.clickWave.active = true;
        mouse.clickWave.x = e.x;
        mouse.clickWave.y = e.y;
        mouse.clickWave.radius = 0;
        mouse.clickWave.lastInteractionRadius = 0; // Réinitialiser la zone d'interaction
        mouse.clickWave.fadeOutPhase = false; // Réinitialiser la phase de disparition
        mouse.clickWave.startTime = Date.now();

        // Définir la couleur de l'onde en fonction de la section actuelle
        switch(currentSection) {
            case 'hero':
                mouse.clickWave.color = 'rgba(0, 240, 255, 0.3)';
                break;
            case 'about':
                mouse.clickWave.color = 'rgba(0, 255, 128, 0.3)';
                break;
            case 'projects':
                mouse.clickWave.color = 'rgba(128, 0, 255, 0.3)';
                break;
            case 'contact':
                mouse.clickWave.color = 'rgba(255, 0, 128, 0.3)';
                break;
            default:
                mouse.clickWave.color = 'rgba(0, 240, 255, 0.3)';
        }

        // Ne pas générer d'explosion de particules
    });

    // Touch interaction for mobile
    window.addEventListener('touchmove', (e) => {
        mouse.x = e.touches[0].clientX;
        mouse.y = e.touches[0].clientY;
        mouse.active = true;

        // Créer plus de particules lors du toucher
        // Augmenter la fréquence de génération (1 chance sur 2)
        if (Math.random() < 0.5) {
            const offsetX = (Math.random() - 0.5) * 20;
            const offsetY = (Math.random() - 0.5) * 20;
            const newParticle = new Particle(mouse.x + offsetX, mouse.y + offsetY);

            newParticle.life = Math.random() * 180 + 120;
            newParticle.fromMouse = true;

            particles.push(newParticle);
            mouse.particles.push(newParticle);

            if (mouse.particles.length > 20) { // Limite augmentée de 15 à 20
                mouse.particles.shift();
            }
        }

        setTimeout(() => {
            mouse.active = false;
        }, 100);
    }, { passive: true });

    // Animation au toucher pour mobile
    window.addEventListener('touchstart', (e) => {
        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;

        // Enregistrer la position du toucher
        mouse.lastClick = { x: touchX, y: touchY };

        // Activer l'onde de choc
        mouse.clickWave.active = true;
        mouse.clickWave.x = touchX;
        mouse.clickWave.y = touchY;
        mouse.clickWave.radius = 0;
        mouse.clickWave.lastInteractionRadius = 0; // Réinitialiser la zone d'interaction
        mouse.clickWave.fadeOutPhase = false; // Réinitialiser la phase de disparition
        mouse.clickWave.startTime = Date.now();

        // Définir la couleur de l'onde en fonction de la section actuelle
        switch(currentSection) {
            case 'hero':
                mouse.clickWave.color = 'rgba(0, 240, 255, 0.3)';
                break;
            case 'about':
                mouse.clickWave.color = 'rgba(0, 255, 128, 0.3)';
                break;
            case 'projects':
                mouse.clickWave.color = 'rgba(128, 0, 255, 0.3)';
                break;
            case 'contact':
                mouse.clickWave.color = 'rgba(255, 0, 128, 0.3)';
                break;
            default:
                mouse.clickWave.color = 'rgba(0, 240, 255, 0.3)';
        }

        // Ne pas générer d'explosion de particules
    }, { passive: true });

    // Initialize particles
    let particles = [];
    function init() {
        particles = [];
        // Augmenter légèrement le nombre de particules initiales
        const particleCount = Math.floor(window.innerWidth / 65); // Densité augmentée

        for (let i = 0; i < particleCount; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            particles.push(new Particle(x, y));
        }
    }

    // Fonction pour dessiner l'onde de choc au clic et interagir avec les particules
    function drawClickWave() {
        if (!mouse.clickWave.active) return;

        // Calculer le temps écoulé depuis le début de l'animation
        const elapsed = Date.now() - mouse.clickWave.startTime;
        const duration = 2000; // Durée totale de l'animation en ms (augmentée pour une transition plus douce)
        const fadeOutStart = 0.7; // Début de la transition de disparition (70% de la durée)

        // Calculer le progrès de l'animation (0 à 1)
        const progress = Math.min(elapsed / duration, 1);

        // Calculer la vitesse de l'onde en fonction du progrès
        // Ralentir progressivement vers la fin pour une transition plus douce
        let currentSpeed;
        if (progress < fadeOutStart) {
            // Vitesse normale jusqu'à fadeOutStart
            currentSpeed = mouse.clickWave.speed;
            mouse.clickWave.fadeOutPhase = false;
        } else {
            // Ralentissement progressif
            const fadeProgress = (progress - fadeOutStart) / (1 - fadeOutStart); // 0 à 1 pendant la phase de disparition
            currentSpeed = mouse.clickWave.speed * (1 - fadeProgress * 0.8); // Réduire jusqu'à 20% de la vitesse initiale
            mouse.clickWave.fadeOutPhase = true;
        }

        // Mettre à jour le rayon avec la vitesse ajustée
        mouse.clickWave.radius += currentSpeed;

        // Calculer l'opacité avec une courbe plus douce
        let opacity;
        if (progress < fadeOutStart) {
            // Opacité légèrement décroissante jusqu'au début de la disparition
            opacity = 1 - (progress / fadeOutStart) * 0.3; // Diminue jusqu'à 0.7
        } else {
            // Disparition progressive avec une courbe plus douce (cubique)
            const fadeProgress = (progress - fadeOutStart) / (1 - fadeOutStart);
            opacity = 0.7 * Math.pow(1 - fadeProgress, 3); // Courbe cubique pour une disparition plus douce
        }

        // Dessiner l'onde avec une épaisseur qui diminue progressivement
        ctx.beginPath();
        ctx.arc(mouse.clickWave.x, mouse.clickWave.y, mouse.clickWave.radius, 0, Math.PI * 2);
        ctx.strokeStyle = mouse.clickWave.color.replace('0.3', opacity.toFixed(2));
        ctx.lineWidth = 2 * opacity;
        ctx.stroke();

        // Dessiner une deuxième onde légèrement plus petite
        ctx.beginPath();
        ctx.arc(mouse.clickWave.x, mouse.clickWave.y, mouse.clickWave.radius * 0.85, 0, Math.PI * 2);
        ctx.strokeStyle = mouse.clickWave.color.replace('0.3', (opacity * 0.7).toFixed(2));
        ctx.lineWidth = 1.5 * opacity;
        ctx.stroke();

        // Interagir avec les particules touchées par l'onde
        interactWithParticles(mouse.clickWave.x, mouse.clickWave.y, mouse.clickWave.radius, progress);

        // Désactiver l'onde lorsque l'animation est terminée
        // Utiliser une condition plus douce basée sur l'opacité et le rayon
        if ((opacity < 0.05 && progress > 0.9) || mouse.clickWave.radius > mouse.clickWave.maxRadius) {
            // Transition finale - faire disparaitre complètement
            if (opacity < 0.02) {
                mouse.clickWave.active = false;
            }
        }
    }

    // Fonction pour faire interagir l'onde avec les particules
    function interactWithParticles(waveX, waveY, waveRadius, progress) {
        // Zone d'effet: anneau entre le rayon actuel et le rayon précédent
        const innerRadius = Math.max(0, waveRadius - mouse.clickWave.speed * 3);
        const outerRadius = waveRadius;

        // Mettre à jour la dernière zone d'interaction pour éviter les doublons
        if (waveRadius <= mouse.clickWave.lastInteractionRadius) {
            return; // Éviter de traiter plusieurs fois la même zone
        }
        mouse.clickWave.lastInteractionRadius = waveRadius;

        // Force de l'interaction avec une courbe plus douce
        let interactionStrength;
        if (progress < 0.7) {
            // Force normale jusqu'à 70% de l'animation
            interactionStrength = 3.5 * (1 - progress * 0.5);
        } else {
            // Décroissance plus douce vers la fin
            const fadeProgress = (progress - 0.7) / 0.3; // 0 à 1 pendant la phase finale
            interactionStrength = 3.5 * (1 - 0.35) * Math.pow(1 - fadeProgress, 2); // Décroissance quadratique
        }

        // Vérifier chaque particule
        for (let i = 0; i < particles.length; i++) {
            const particle = particles[i];

            // Calculer la distance entre la particule et le centre de l'onde
            const dx = particle.x - waveX;
            const dy = particle.y - waveY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // Vérifier si la particule est dans la zone d'effet (anneau)
            if (distance >= innerRadius && distance <= outerRadius) {
                // Calculer l'angle entre le centre de l'onde et la particule
                const angle = Math.atan2(dy, dx);

                // Appliquer une force qui pousse la particule vers l'extérieur
                const pushForce = interactionStrength * (1 - (distance - innerRadius) / (outerRadius - innerRadius));

                // Mettre à jour la vitesse de la particule
                particle.speedX += Math.cos(angle) * pushForce;
                particle.speedY += Math.sin(angle) * pushForce;

                // Augmenter temporairement la taille de la particule
                particle.size *= 1.3; // Augmentation plus importante
                particle.size = Math.min(particle.size, 10); // Taille maximale plus grande

                // Marquer la particule comme ayant interagi
                particle.interacted = true;
                particle.interactionTime = Date.now();

                // Changer temporairement la couleur de la particule
                if (!particle.originalColor) {
                    particle.originalColor = particle.color;

                    // Extraire la teinte de la couleur de l'onde
                    let waveHue;
                    switch(currentSection) {
                        case 'hero':
                            waveHue = 190; // Bleu/cyan
                            break;
                        case 'about':
                            waveHue = 140; // Vert/cyan
                            break;
                        case 'projects':
                            waveHue = 270; // Bleu/violet
                            break;
                        case 'contact':
                            waveHue = 330; // Violet/rose
                            break;
                        default:
                            waveHue = 190;
                    }

                    // Appliquer une couleur plus vive avec effet lumineux
                    particle.color = `hsl(${waveHue}, 100%, 80%)`;

                    // Ajouter un effet de traînée temporaire
                    particle.trail = true;

                    // Restaurer la couleur d'origine après un délai
                    setTimeout(() => {
                        if (particle && particle.originalColor) {
                            particle.color = particle.originalColor;
                            delete particle.originalColor;
                            particle.trail = false;
                        }
                    }, 800); // Délai plus long
                }

                // Créer un effet de connexion entre les particules affectées
                if (Math.random() < 0.3) { // 30% de chance de créer une connexion
                    for (let j = 0; j < particles.length; j++) {
                        if (i !== j && particles[j].interacted && Date.now() - particles[j].interactionTime < 500) {
                            const otherParticle = particles[j];
                            const dx2 = particle.x - otherParticle.x;
                            const dy2 = particle.y - otherParticle.y;
                            const distance2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);

                            // Connecter uniquement les particules proches
                            if (distance2 < 100) {
                                // Dessiner une connexion spéciale
                                ctx.beginPath();
                                ctx.moveTo(particle.x, particle.y);
                                ctx.lineTo(otherParticle.x, otherParticle.y);

                                // Couleur de la connexion basée sur la section
                                let connectionColor;
                                switch(currentSection) {
                                    case 'hero':
                                        connectionColor = 'rgba(0, 240, 255, 0.6)';
                                        break;
                                    case 'about':
                                        connectionColor = 'rgba(0, 255, 128, 0.6)';
                                        break;
                                    case 'projects':
                                        connectionColor = 'rgba(128, 0, 255, 0.6)';
                                        break;
                                    case 'contact':
                                        connectionColor = 'rgba(255, 0, 128, 0.6)';
                                        break;
                                    default:
                                        connectionColor = 'rgba(0, 240, 255, 0.6)';
                                }

                                ctx.strokeStyle = connectionColor;
                                ctx.lineWidth = 1.5;
                                ctx.stroke();

                                // Limiter le nombre de connexions
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Mettre à jour l'apparence des particules qui ont interagi
        for (let i = 0; i < particles.length; i++) {
            const particle = particles[i];

            // Réduire progressivement la taille des particules qui ont interagi
            if (particle.interacted && particle.size > particle.originalSize) {
                particle.size *= 0.96; // Réduction plus rapide
                if (particle.size <= particle.originalSize) {
                    particle.size = particle.originalSize;
                }
            }

            // Dessiner une traînée derrière les particules affectées
            if (particle.trail && particle.interacted) {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size * 1.5, 0, Math.PI * 2);
                ctx.fillStyle = particle.color.replace(')', ', 0.2)');
                ctx.fill();
            }
        }
    }

    init();

    // Fonction spécifique pour connecter les particules générées par la souris
    function drawMouseConnections() {
        if (mouse.particles.length < 2) return;

        // Connecter les particules de la souris entre elles de manière aléatoire
        for (let i = 0; i < mouse.particles.length; i++) {
            // Nombre aléatoire de connexions pour chaque particule
            const numConnections = Math.floor(Math.random() * 3) + 1;
            let connectedCount = 0;

            // Essayer de connecter à d'autres particules de souris
            for (let j = 0; j < mouse.particles.length; j++) {
                if (i === j || connectedCount >= numConnections) continue;

                // Connexion aléatoire avec une probabilité de 30%
                if (Math.random() < 0.3) {
                    const p1 = mouse.particles[i];
                    const p2 = mouse.particles[j];

                    if (!p1 || !p2 || p1.life <= 0 || p2.life <= 0) continue;

                    const dx = p1.x - p2.x;
                    const dy = p1.y - p2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    // Connecter uniquement si la distance est raisonnable
                    if (distance < mouse.radius) {
                        connectedCount++;

                        // Couleur spéciale pour les connexions de souris - opacité augmentée
                        const opacityFactor = 0.6 * (1 - distance / mouse.radius);
                        const startColor = `rgba(255, 255, 255, ${opacityFactor})`;
                        const endColor = `rgba(0, 240, 255, ${opacityFactor * 0.8})`;

                        // Effet de pulsation - intensité augmentée
                        const pulseSpeed = 0.003;
                        const pulseIntensity = 0.4;
                        const pulseFactor = 1 + Math.sin(Date.now() * pulseSpeed) * pulseIntensity;

                        // Créer un gradient pour la ligne
                        const gradient = ctx.createLinearGradient(
                            p1.x, p1.y, p2.x, p2.y
                        );
                        gradient.addColorStop(0, startColor);
                        gradient.addColorStop(1, endColor);

                        ctx.beginPath();
                        ctx.strokeStyle = gradient;
                        ctx.lineWidth = 1.5 * pulseFactor; // Épaisseur augmentée

                        // Lignes en pointillés pour toutes les sections - motif ajusté
                        ctx.setLineDash([6 * pulseFactor, 7 * (1 - chaosLevel * 0.5)]);

                        // Effet de lueur - intensité augmentée
                        ctx.shadowBlur = 10 * pulseFactor;
                        ctx.shadowColor = 'rgba(0, 240, 255, 0.6)';

                        // Ligne droite ou courbe (aléatoire)
                        if (Math.random() < 0.5) {
                            // Ligne courbe
                            const midX = (p1.x + p2.x) / 2;
                            const midY = (p1.y + p2.y) / 2;
                            const offset = (Math.random() - 0.5) * 30;

                            ctx.moveTo(p1.x, p1.y);
                            ctx.quadraticCurveTo(
                                midX + offset,
                                midY + offset,
                                p2.x,
                                p2.y
                            );
                        } else {
                            // Ligne droite
                            ctx.moveTo(p1.x, p1.y);
                            ctx.lineTo(p2.x, p2.y);
                        }

                        ctx.stroke();

                        // Réinitialiser les effets
                        ctx.shadowBlur = 0;
                    }
                }
            }

            // Connecter également aux particules normales proches
            for (let j = 0; j < particles.length; j++) {
                if (connectedCount >= numConnections + 2) break;

                // Ne pas connecter aux particules de souris déjà traitées
                if (particles[j].fromMouse) continue;

                // Connexion aléatoire avec une probabilité de 15%
                if (Math.random() < 0.15) {
                    const p1 = mouse.particles[i];
                    const p2 = particles[j];

                    if (!p1 || !p2 || p1.life <= 0 || p2.life <= 0) continue;

                    const dx = p1.x - p2.x;
                    const dy = p1.y - p2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < mouse.radius * 0.7) {
                        connectedCount++;

                        // Couleur différente pour ces connexions
                        const opacityFactor = 0.3 * (1 - distance / (mouse.radius * 0.7));
                        const color = `rgba(180, 180, 255, ${opacityFactor})`;

                        ctx.beginPath();
                        ctx.strokeStyle = color;
                        ctx.lineWidth = 0.8;

                        // Lignes en pointillés pour toutes les sections
                        ctx.setLineDash([4, 6]);

                        ctx.moveTo(p1.x, p1.y);
                        ctx.lineTo(p2.x, p2.y);
                        ctx.stroke();

                        // Réinitialiser les lignes en pointillés
                        ctx.setLineDash([]);
                    }
                }
            }
        }
    }

    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Background gradient adapté à la section actuelle
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);

        switch(currentSection) {
            case 'hero':
                gradient.addColorStop(0, 'rgba(10, 10, 20, 1)');
                gradient.addColorStop(1, 'rgba(5, 5, 8, 1)');
                break;
            case 'about':
                gradient.addColorStop(0, 'rgba(10, 15, 20, 1)');
                gradient.addColorStop(1, 'rgba(5, 8, 10, 1)');
                break;
            case 'projects':
                gradient.addColorStop(0, 'rgba(15, 10, 25, 1)');
                gradient.addColorStop(1, 'rgba(8, 5, 15, 1)');
                break;
            case 'contact':
                gradient.addColorStop(0, 'rgba(20, 10, 20, 1)');
                gradient.addColorStop(1, 'rgba(10, 5, 10, 1)');
                break;
            default:
                gradient.addColorStop(0, 'rgba(10, 10, 20, 1)');
                gradient.addColorStop(1, 'rgba(5, 5, 8, 1)');
        }

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Update and draw particles
        for (let i = 0; i < particles.length; i++) {
            particles[i].update();
            particles[i].draw();

            // Remove dead particles
            if (particles[i].life <= 0) {
                // Retirer également de la liste des particules de souris si nécessaire
                if (particles[i].fromMouse) {
                    const mouseIndex = mouse.particles.indexOf(particles[i]);
                    if (mouseIndex !== -1) {
                        mouse.particles.splice(mouseIndex, 1);
                    }
                }

                particles.splice(i, 1);
                i--;
            }
        }

        // Draw connections
        drawConnections(particles);

        // Dessiner les connexions spéciales pour les particules de souris
        drawMouseConnections();

        // Dessiner l'onde de choc au clic si active
        if (mouse.clickWave.active) {
            drawClickWave();
        }

        // Génération progressive des particules pour toutes les sections - légèrement augmentée
        let particleGenerationRate = 0.015 + (chaosLevel * 0.04); // Taux de génération augmenté
        let maxParticles = 80 + (chaosLevel * 50); // Nombre maximum de particules augmenté

        // Add new particles occasionally
        if (Math.random() < particleGenerationRate && particles.length < maxParticles) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            particles.push(new Particle(x, y));
        }

        requestAnimationFrame(animate);
    }

    animate();

    // Ajouter l'effet de vagues uniquement dans certaines sections
    function animateWithWaves() {
        // Afficher les vagues uniquement dans les sections avec chaos élevé
        if (chaosLevel > 0.3) {
            drawWaves();
        }
        requestAnimationFrame(animateWithWaves);
    }

    animateWithWaves();

    // Add wave effect
    let time = 0;
    function drawWaves() {
        // Progression plus lente du temps pour éviter le scintillement
        time += 0.005 * (1 + chaosLevel);

        // Ajuster l'amplitude et la fréquence des vagues en fonction du chaos
        const amplitude = 20 * (1 + chaosLevel);
        const frequency = 0.01 * (1 + chaosLevel * 0.3);
        const step = Math.max(3, 6 - Math.floor(chaosLevel * 2)); // Densité des points réduite

        // Couleur des vagues basée sur la section actuelle
        let waveColor;
        switch(currentSection) {
            case 'hero':
                waveColor = 'rgba(0, 240, 255, 0.3)';
                break;
            case 'about':
                waveColor = 'rgba(0, 255, 128, 0.3)';
                break;
            case 'projects':
                waveColor = 'rgba(128, 0, 255, 0.3)';
                break;
            case 'contact':
                waveColor = 'rgba(255, 0, 128, 0.3)';
                break;
            default:
                waveColor = 'rgba(0, 240, 255, 0.3)';
        }

        // Dessiner les vagues avec moins de variations aléatoires
        for (let i = 0; i < canvas.width; i += step) {
            // Utiliser une fonction déterministe pour les perturbations
            const chaosOffset = chaosLevel > 0.3 ?
                Math.sin(i * 0.1 + time * 2) * chaosLevel * 20 : 0;

            const y = Math.sin(i * frequency + time) * amplitude + canvas.height / 2 + chaosOffset;

            ctx.beginPath();
            const pointSize = 1 + (chaosLevel * 1.2);
            ctx.arc(i, y, pointSize, 0, Math.PI * 2);
            ctx.fillStyle = waveColor;
            ctx.fill();

            // Ajouter des lignes entre les points de manière plus stable
            if (chaosLevel > 0.6 && i > 0 && i < canvas.width - step) {
                const prevY = Math.sin((i - step) * frequency + time) * amplitude + canvas.height / 2;
                const nextY = Math.sin((i + step) * frequency + time) * amplitude + canvas.height / 2;

                // Utiliser une condition déterministe plutôt qu'aléatoire
                if ((i % 3) === 0) {
                    ctx.beginPath();
                    ctx.moveTo(i - step, prevY);
                    ctx.lineTo(i + step, nextY);
                    ctx.strokeStyle = waveColor.replace('0.3', '0.15');
                    ctx.lineWidth = 0.5;
                    ctx.stroke();
                }
            }
        }
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        resizeCanvas();
        init();
    });
});
