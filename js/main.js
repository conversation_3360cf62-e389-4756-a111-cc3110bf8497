document.addEventListener('DOMContentLoaded', () => {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add ripple effect to tech-item buttons
    document.querySelectorAll('.tech-item').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple element
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');

            // Position the ripple
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            // Apply styles
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            // Add to button
            this.appendChild(ripple);

            // Remove after animation
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Simple fade-in animation for paragraphs
    function animateTextWithFade(element) {
        if (!element) return;

        // Add fade-in class
        element.classList.add('fade-in-text');
    }

    // Apply fade-in animation to all paragraphs
    document.querySelectorAll('p:not(.subtitle)').forEach(paragraph => {
        // Create an observer for each paragraph
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateTextWithFade(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        observer.observe(paragraph);
    });

    // Special handling for about-text when changed by tech-item buttons
    const originalChangeAboutText = window.changeAboutText;
    if (originalChangeAboutText) {
        window.changeAboutText = function(tech) {
            const aboutText = document.getElementById('about-text');
            if (!aboutText) return;

            // Remove animation class first
            aboutText.classList.remove('fade-in-text');

            // Call the original function to set the text content
            originalChangeAboutText(tech);

            // Force a reflow to restart the animation
            void aboutText.offsetWidth;

            // Add animation class again
            aboutText.classList.add('fade-in-text');
        };
    }

    // Glitch effect on hover for the main title
    const glitchText = document.querySelector('.glitch-text');
    if (glitchText) {
        glitchText.addEventListener('mouseover', () => {
            glitchText.style.animation = 'none';
            setTimeout(() => {
                glitchText.style.animation = 'textShadow 2s infinite alternate';
            }, 10);
        });
    }

    // Interactive CTA buttons
    const ctaButtons = document.querySelectorAll('.cta-button');
    ctaButtons.forEach(button => {
        button.addEventListener('mouseover', () => {
            button.style.transform = 'translateY(-5px)';
            button.style.boxShadow = '0 10px 25px rgba(0, 240, 255, 0.5)';
        });

        button.addEventListener('mouseout', () => {
            button.style.transform = '';
            button.style.boxShadow = '';
        });
    });

    // Primary CTA button action
    const primaryCta = document.querySelector('.cta-button.primary');
    if (primaryCta) {
        primaryCta.addEventListener('click', () => {
            const projectsSection = document.getElementById('personal-projects');
            if (projectsSection) {
                window.scrollTo({
                    top: projectsSection.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    }

    // Secondary CTA button action
    const secondaryCta = document.querySelector('.cta-button.secondary');
    if (secondaryCta) {
        secondaryCta.addEventListener('click', () => {
            const contactSection = document.getElementById('contact');
            if (contactSection) {
                window.scrollTo({
                    top: contactSection.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    }

    // Form submission handling
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Get form data
            const formData = new FormData(contactForm);
            const formValues = Object.fromEntries(formData.entries());

            // Here you would typically send the data to a server
            console.log('Form submitted:', formValues);

            // Show success message (in a real app, this would happen after successful submission)
            alert('Message sent successfully! I will get back to you soon.');
            contactForm.reset();
        });
    }

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe sections for scroll animations
    document.querySelectorAll('section').forEach(section => {
        section.style.opacity = '1';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });

    // Add 'visible' class for animation
    document.querySelectorAll('.visible').forEach(el => {
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
    });

    // Typing effect for subtitle
    const subtitle = document.querySelector('.subtitle');
    if (subtitle) {
        const text = subtitle.textContent;
        subtitle.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                subtitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 50);
            }
        };

        // Start typing effect after a short delay
        setTimeout(typeWriter, 1000);
    }
});
