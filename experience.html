<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expériences | Tym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/experience.css">
    <link rel="stylesheet" href="css/dropdown.css">
    <link rel="icon" href="assets/favicon.ico" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Interactive Background Canvas -->
    <canvas id="background-canvas"></canvas>

    <!-- Main Content -->
    <main class="container">
        <header class="header">
            <a href="index.html" class="logo" style="text-decoration: none;">TYM.IA</a>
            <nav class="nav">
                <ul>
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="experience.html" class="nav-link active">Expériences</a></li>
                    <li class="nav-item">
                        <a href="index.html#personal-projects" class="nav-link dropdown-toggle">Projets</a>
                        <div class="dropdown-menu">
                            <a href="yachef.html" class="dropdown-item">YaChef</a>
                            <a href="sacorp.html" class="dropdown-item">SAcorp</a>
                            <a href="voyage.html" class="dropdown-item">Stop Arnaque Voyage</a>
                        </div>
                    </li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
        </header>

        <section class="experience-hero">
            <div class="experience-hero-content">
                <h1 class="glitch-text">Parcours & <span class="highlight">Compétences</span></h1>
                <p class="subtitle">Mon expérience professionnelle et mes formations</p>
                <div class="cta-container" style="justify-content: center;">
                    <a href="TymotheLaineCV.pdf" download class="cta-button primary">Télécharger mon CV</a>
                </div>
            </div>
        </section>

        <section id="formation" class="experience-section">
            <h2 class="section-title">Formation</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2019 - 2021</div>
                        <h3>Master E3A Spécialisé en Intelligence Artificielle</h3>
                        <p>Paris Saclays</p>
                        <p>Spécialisation en apprentissage profond et traitement du langage naturel.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2018 - 2019</div>
                        <h3>Licence Siences pour l'ingénieur</h3>
                        <p>Université d'évry, évry</p>
                        <p>Formation d'ingénieur spécialisé en robotique industriel.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2016 - 2018</div>
                        <h3>DUT Génie mécanique et productique</h3>
                        <p>IUT de l'Aisne, Saint-Quentin</p>
                        <p>Formation de mécanique et de production de systemes.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2016</div>
                        <h3>Baccalauréat Scientifique</h3>
                        <p>Lycée Francois Couperin, Fontainebleau</p>
                        <p>Option informatique appliquée</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="professional" class="experience-section">
            <h2 class="section-title">Expérience Professionnelle</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024 - Actuel</div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h3><a href="https://www.diptick.fr" target="_blank" style="text-decoration: none; color: inherit;">Diptick</a></h3>
                            <a href="https://www.diptick.fr" target="_blank" class="cta-button primary" style="display: inline-block;">Visiter le site</a>
                        </div>
                        <p>Développeur Fullstack</p>
                        <p>Développement d'un SaaS de billetterie pour les lieux culturels français, couvrant tous les aspects du cycle de développement logiciel.</p>
                        <p></p>
                        <ul class="experience-details">
                            <li>Développement backend robuste et évolutif pour des applications à forte charge</li>
                            <li>Création d'interfaces frontend modernes et réactives</li>
                            <li>Gestion de serveurs et optimisation des performances</li>
                            <li>Conception et administration de bases de données</li>
                            <li>Mise en place de processus CI/CD pour un déploiement continu et fiable</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2021 - 2023</div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h3><a href="https://www.lecole.ai" target="_blank" style="text-decoration: none; color: inherit;">L'École</a></h3>
                            <a href="https://www.lecole.ai" target="_blank" class="cta-button primary" style="display: inline-block;">Visiter le site</a>
                        </div>
                        <p>Responsable de l'Intelligence Artificielle et de l'Algorithme Automatique</p>
                        <p>Expérience au sein d'une startup de 4 personnes, développant une application iPad pour la création d'IA personnels.</p>
                        <ul class="experience-details">
                            <li>Conception et développement d'algorithmes d'apprentissage automatique pour IA personnalisée</li>
                            <li>Gestion du cycle de vie complet des projets d'IA pour l'application iPad</li>
                            <li>Collaboration étroite avec l'équipe de développement mobile et UX</li>
                            <li>Optimisation des performances des modèles d'IA pour une utilisation sur tablette</li>
                            <li>Veille technologique et intégration des dernières avancées en IA mobile</li>
                            <li>Présentations et démonstrations de l'application aux investisseurs et partenaires</li>
                            <li>Développement de fonctionnalités innovantes pour la création d'IA sur iPad</li>
                            <li>Déploiement et management de l'infrastructure AWS pour l'application</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="freelance" class="experience-section">
            <h2 class="section-title">Projets Freelance</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024</div>
                        <h3>Site web de prévention pour les entreprises du voyage</h3>
                        <p>Collaboration et Développement Web</p>
                        <p>Collaboration avec l'équipe de SAcorp pour la réalisation d'un site web de prévention (stop arnaque voyage).</p>
                        <ul class="experience-details">
                            <li>Design et réalisation du site web</li>
                            <li>Conception d'une interface utilisateur intuitive pour faciliter l'accès à l'information</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">  
                        <div class="timeline-date">2023-2024</div>
                        <h3>Conception et réalisation d'un SaaS de visualisation de datas pour l'entreprise SAcorp</h3>
                        <p>Développement backend</p>
                        <p>Réalisation du projet en collaboration avec l'équipe de dévbeloppement de SAcorp.</p>
                        <ul class="experience-details">
                            <li>Conception et réalisation du backend.</li>
                            <li>Intégration des API metaAds, TikTokAds, GoogleAds pour la récupération de datas automatique.</li>
                            <li>Définition et création de la base de données.</li>
                            <li>Authentification client et gestions de droits.</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2023</div>
                        <h3>Conception et réalisation du site web de SAcorp</h3>
                        <p>Développement Web</p>
                        <p>Création complète d'un site web professionnel pour SAcorp, mettant en valeur leurs services et leur expertise.</p>
                        <ul class="experience-details">
                            <li>Design responsive et moderne</li>
                            <li>Optimisation SEO pour une meilleure visibilité en ligne</li>
                            <li>Intégration de fonctionnalités interactives pour améliorer l'engagement des utilisateurs</li>
                        </ul>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2021</div>
                        <h3>Formation Unity3D pour le développement logiciel</h3>
                        <p>Formation et Mentorat</p>
                        <p>Formation de personnel sur le développement logiciel Unity, permettant à l'équipe d'acquérir de nouvelles compétences en développement de jeux et d'applications 3D.</p>
                        <ul class="experience-details">
                            <li>Élaboration d'un programme de formation personnalisé</li>
                            <li>Enseignement des principes fondamentaux de Unity3D et des meilleures pratiques</li>
                            <li>Accompagnement pratique sur des projets concrets</li>
                            <li>Évaluation des progrès et feedback continu</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="internships" class="experience-section">
            <h2 class="section-title">Stages</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2021</div>
                        <h3>Système interactif en réalité virtuelle</h3>
                        <p>Thales Alenia Space</p>
                        <p>Conception d'un système interactif en réalité virtuelle pour la visualisation et conception de systèmes avant la mise en place sur banc d'essais.</p>
                        <ul class="experience-details">
                            <li>Développement d'interfaces en réalité virtuelle pour l'industrie spatiale</li>
                            <li>Conception de solutions de visualisation 3D pour prototypage virtuel</li>
                            <li>Optimisation des processus de conception avant tests physiques</li>
                            <li>Collaboration avec les équipes d'ingénierie spatiale</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-date">2018</div>
                        <h3>Conception de pièces mécaniques pour véhicules blindés</h3>
                        <p>Panhard Général Défense (Arquus)</p>
                        <p>Stage de conception mécanique pour véhicules militaires spécialisés.</p>
                        <ul class="experience-details">
                            <li>Conception de pièces mécaniques pour véhicules blindés</li>
                            <li>Analyse technique sur véhicules existants</li>
                            <li>Prise de mesures précises sur ligne de production</li>
                            <li>Collaboration avec l'équipe d'ingénierie de défense</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="skills" class="experience-section">
            <h2 class="section-title">Compétences Techniques</h2>

            <div class="skills-container">
                <div class="skills-category">
                    <h3>Langages de Programmation</h3>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-name">Python</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 95%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">JavaScript</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 90%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">SQL</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 70%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Bash script</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 75%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skills-category">
                    <h3>Frameworks & Bibliothèques</h3>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-name">Django</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 93%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Intégration d'API</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 85%;"></div>
                            </div>
                        </div>

                        <div class="skill-item">
                            <div class="skill-name">ReactJS</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 85%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">PyTorch</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">TensorFlow</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">BeamPipeline</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skills-category">
                    <h3>Intelligence Artificielle</h3>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-name">Utilisation de l'IA pour l'aide au developpement</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 95%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Deep Learning</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 90%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Computer Vision</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skills-category">
                    <h3>Outils & Plateformes</h3>
                    <div class="skills-grid">
                        <div class="skill-item">
                            <div class="skill-name">Git</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 95%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Linux</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 85%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">CI/CD</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Docker</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 70%;"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">AWS</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 70%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="languages" class="experience-section">
            <h2 class="section-title">Langues</h2>
            <div class="languages-container">
                <div class="language-item">
                    <h3>Français</h3>
                    <div class="language-level">
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                    </div>
                    <p>Langue maternelle</p>
                </div>
                <div class="language-item">
                    <h3>Anglais</h3>
                    <div class="language-level">
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot active"></span>
                        <span class="language-dot"></span>
                    </div>
                    <p>Courant</p>
                </div>
            </div>
        </section>

        <section id="certifications" class="experience-section">
            <h2 class="section-title">Certifications</h2>
            <div class="certifications-container">
                <div class="certification-item">
                    <div class="certification-icon">
                        <div class="certification-badge"></div>
                    </div>
                    <div class="certification-content">
                        <h3>Gestion de Projets Génie logiciel</h3>
                        <p class="certification-issuer">Visiplus</p>
                        <p class="certification-date">2025</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2025 TYMOTHE LAINE - Tous droits de réserver</p>
            <div class="social-links">
                <a href="contact.html" class="social-link">Contact</a>
            </div>
        </div>
    </footer>

    <script src="js/background.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
