<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YaChef - Assistant de cuisine IA | Tym</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/project.css">
    <link rel="stylesheet" href="css/dropdown.css">
    <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Interactive Background Canvas -->
    <canvas id="background-canvas"></canvas>

    <!-- Main Content -->
    <main class="container">
        <header class="header">
            <a href="index.html" class="logo" style="text-decoration: none;">TYM.IA</a>
            <nav class="nav">
                <ul>
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="experience.html" class="nav-link">Expériences</a></li>
                    <li class="nav-item">
                        <a href="index.html#personal-projects" class="nav-link dropdown-toggle active">Projets</a>
                        <div class="dropdown-menu">
                            <a href="yachef.html" class="dropdown-item active">YaChef</a>
                            <a href="sacorp.html" class="dropdown-item">SAcorp</a>
                            <a href="voyage.html" class="dropdown-item">Stop Arnaque Voyage</a>
                        </div>
                    </li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
        </header>

        <section class="project-hero">
            <div class="project-hero-content">
                <div class="project-logo">YC</div>
                <h1 class="glitch-text">YaChef<span class="highlight">.ai</span></h1>
                <p class="project-subtitle">Assistant de cuisine intelligent propulsé par l'IA</p>
                <div class="project-links">
                    <a href="https://yachef.ai" target="_blank" class="project-link-button primary">Visiter le site <span>↗</span></a>
                    <a href="contact.html" class="project-link-button secondary">Me contacter</a>
                </div>
            </div>
        </section>

        <section class="project-section">
            <h2 class="project-section-title">Présentation du projet</h2>
            <div class="project-content">
                <p>
                    <strong>YaChef</strong> est un assistant de cuisine intelligent propulsé par l'intelligence artificielle, conçu pour révolutionner l'expérience culinaire des utilisateurs. Ce projet personnel est né de ma passion pour la cuisine et les technologies d'IA, avec l'objectif de créer un outil qui rend la cuisine plus accessible, créative et personnalisée.
                </p>
                <p>
                    L'assistant YaChef analyse les préférences alimentaires, les restrictions diététiques et les ingrédients disponibles pour proposer des recettes sur mesure. Grâce à des algorithmes d'apprentissage avancés, YaChef s'améliore continuellement en fonction des interactions avec les utilisateurs, offrant des suggestions toujours plus pertinentes.
                </p>
                <p>
                    Ce projet représente l'aboutissement de mes compétences en développement d'IA et en création d'interfaces utilisateur intuitives, avec une attention particulière portée à l'expérience utilisateur et à l'accessibilité.
                </p>
            </div>
        </section>

        <section class="project-section">
            <h2 class="project-section-title">Fonctionnalités clés</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <h3>IA conversationnelle</h3>
                    <p>Interface de dialogue naturel permettant aux utilisateurs d'interagir avec l'assistant comme avec un véritable chef cuisinier.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🍲</div>
                    <h3>Recettes sur mesure</h3>
                    <p>Génération de recettes adaptées aux préférences, restrictions alimentaires et ingrédients disponibles de l'utilisateur.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Multi-plateforme</h3>
                    <p>Disponible sur web, mobile et comme assistant vocal pour une expérience culinaire sans interruption.</p>
                </div>
            </div>
        </section>

        <section class="project-section">
            <h2 class="project-section-title">Technologies utilisées</h2>
            <div class="project-content">
                <p>YaChef a été développé en utilisant un ensemble de technologies modernes pour garantir performance, évolutivité et expérience utilisateur optimale :</p>
                <div class="tech-grid">
                    <span class="tech-badge">Python</span>
                    <span class="tech-badge">Django</span>
                    <span class="tech-badge">GPT API</span>
                    <span class="tech-badge">Postgres</span>
                    <span class="tech-badge">AWS</span>
                    <span class="tech-badge">Docker</span>
                    <span class="tech-badge">WebSockets</span>
                    <span class="tech-badge">Gestion de serveur</span>
                    <span class="tech-badge">Bash</span>
                    <span class="tech-badge">Suivi gunicorn</span>
                    <span class="tech-badge">Gesion de mail</span>
                </div>
            </div>
        </section>

        <section class="project-section">
            <h2 class="project-section-title">Processus de développement</h2>
            <div class="process-timeline">
                <div class="process-step">
                    <div class="process-dot"></div>
                    <div class="process-content">
                        <h3>Recherche et conception</h3>
                        <p>Étude approfondie des besoins des utilisateurs en matière d'assistance culinaire et des technologies d'IA disponibles.</p>
                        <p>Analyse des besoins utilisateurs, étude des technologies d’IA adaptées, conception de l’architecture système, définition des flux d’interaction et élaboration du cahier des charges.</p>
                    </div>
                </div>
                <div class="process-step">
                    <div class="process-dot"></div>
                    <div class="process-content">
                        <h3>Conception et développement</h3>
                        <p>Mise en place de l'architecture logicielle et développement des fonctionnalités clés de l'application.</p>
                        <p>Développement complet d’un SaaS incluant le choix des technologies, la création d’un backend Django avec intégration de GPT-4, une API RESTful, une base de données PostgreSQL avec cache Redis, un frontend responsive, et le déploiement sur AWS avec Docker pour la scalabilité.</p>
                    </div>
                </div>
                <div class="process-step">
                    <div class="process-dot"></div>
                    <div class="process-content">
                        <h3>Création de l'interface utilisateur</h3>
                        <p>Développement d'une interface intuitive et réactive, avec un focus sur l'accessibilité et l'expérience utilisateur. Tests utilisateurs itératifs pour affiner les interactions.</p>
                    </div>
                </div>
                <div class="process-step">
                    <div class="process-dot"></div>
                    <div class="process-content">
                        <h3>Intégration et tests</h3>
                        <p>Connexion du frontend avec les API d'IA et les bases de données. Tests approfondis de performance, de sécurité et d'expérience utilisateur sur différentes plateformes.</p>
                    </div>
                </div>
                <div class="process-step">
                    <div class="process-dot"></div>
                    <div class="process-content">
                        <h3>Lancement et amélioration continue</h3>
                        <p>Déploiement de la version bêta, collecte de retours utilisateurs et itérations régulières pour améliorer les fonctionnalités et la précision de l'IA.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta-section">
            <div class="cta-content">
                <h2>Prêt à révolutionner votre expérience culinaire ?</h2>
                <p>Découvrez YaChef dès maintenant et transformez votre façon de cuisiner avec l'aide de l'intelligence artificielle.</p>
                <div class="cta-container" style="justify-content: center;">
                    <a href="https://yachef.ai" target="_blank" class="cta-button primary">Essayer YaChef <span>↗</span></a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 TYM.IA - Tous droits réservés</p>
            <div class="social-links">
                <a href="contact.html" class="social-link">Contact</a>
            </div>
        </div>
    </footer>

    <script src="js/background.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
